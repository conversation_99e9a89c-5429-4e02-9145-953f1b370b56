#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试脚本
验证修改后的功能是否正常工作
"""

def test_url_construction():
    """
    测试URL构建逻辑
    """
    print("=== 测试URL构建逻辑 ===")
    
    # 测试参数
    course_id = "221247981"
    class_id = "122555170" 
    exam_id = "6991302"
    cpi = "356795813"
    validate_str = "validate_Ew0z9skxsLzVKQjmeObQiRVLxkxbPkRF_E80A7BE63BC18F3DB2BA17C747096A65"
    
    # 当前的URL构建方式
    from urllib.parse import urlencode
    
    exam_page_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"
    params = {
        "courseId": course_id,
        "classId": class_id,
        "examId": exam_id,
        "cpi": cpi,
        "validate": validate_str
    }
    
    current_url = f"{exam_page_url}?{urlencode(params)}"
    print(f"当前构建的URL: {current_url}")
    
    # 期望的URL格式（基于用户提供的信息）
    expected_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={course_id}&classId={class_id}&tId={exam_id}&id=160210345&p=1&tag=1&enc=ff2516cd4657243633c6a3f0eeb8c098&cpi={cpi}&openc=687cf461734107a032056957d0b52d39&newMooc=true"
    print(f"期望的URL格式: {expected_url}")
    
    print("\n关键差异:")
    print("1. examId vs tId")
    print("2. 缺少动态参数: id, p, tag, enc, openc, newMooc")
    print("3. 这些参数需要从验证码成功后的响应中获取")

def test_import_modules():
    """
    测试模块导入
    """
    print("\n=== 测试模块导入 ===")
    
    try:
        from SimpleLoginManager import SimpleLoginManager
        print("✅ SimpleLoginManager 导入成功")
    except Exception as e:
        print(f"❌ SimpleLoginManager 导入失败: {e}")
    
    try:
        from ExamCaptchaHandler import ExamCaptchaHandler
        print("✅ ExamCaptchaHandler 导入成功")
    except Exception as e:
        print(f"❌ ExamCaptchaHandler 导入失败: {e}")
    
    try:
        from CaptchaHandler import CaptchaHandler
        print("✅ CaptchaHandler 导入成功")
    except Exception as e:
        print(f"❌ CaptchaHandler 导入失败: {e}")

def analyze_problem():
    """
    分析问题
    """
    print("\n=== 问题分析 ===")
    
    print("用户反馈的问题:")
    print("1. 验证码成功后没有自动跳转到真正的考试页面")
    print("2. 返回的是拼接的URL，而不是真实的考试页面URL")
    print("3. 期望获取包含完整参数的URL")
    
    print("\n解决方案:")
    print("1. ✅ 修改了 get_exam_page_url 方法，实际请求考试页面")
    print("2. ✅ 添加了页面跳转等待逻辑")
    print("3. ✅ 增加了多种方式获取考试URL")
    print("4. ✅ 添加了响应解析逻辑")
    
    print("\n可能的改进:")
    print("1. 需要更准确地解析考试页面响应")
    print("2. 可能需要模拟更多的前置请求")
    print("3. 考虑添加Cookie和Session状态管理")

def main():
    """
    主函数
    """
    print("快速测试开始...\n")
    
    # 测试URL构建
    test_url_construction()
    
    # 测试模块导入
    test_import_modules()
    
    # 分析问题
    analyze_problem()
    
    print("\n=== 测试完成 ===")
    print("建议:")
    print("1. 验证修改后的代码是否能正确处理验证码成功后的跳转")
    print("2. 检查是否需要额外的API调用来获取完整的考试参数")
    print("3. 考虑添加更详细的调试信息")

if __name__ == "__main__":
    main()
