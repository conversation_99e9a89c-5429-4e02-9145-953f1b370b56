#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试考试流程脚本
用于验证修改后的考试滑块验证码处理功能
"""

import sys
import os
import time
import requests

def test_exam_url_access(exam_url):
    """
    测试考试URL是否可以正常访问
    """
    try:
        print(f"测试访问考试URL: {exam_url}")
        
        session = requests.Session()
        session.verify = False
        
        response = session.get(exam_url, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"最终URL: {response.url}")
        
        # 检查是否是考试页面
        exam_indicators = [
            "考试",
            "题目", 
            "examPaper",
            "questionLi",
            "TiMu",
            "exam-container",
            "reVersionTestStartNew"
        ]
        
        found_indicators = []
        for indicator in exam_indicators:
            if indicator in response.text or indicator in response.url:
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"✅ 检测到考试页面标识: {found_indicators}")
            return True
        else:
            print("❌ 未检测到考试页面标识")
            return False
            
    except Exception as e:
        print(f"测试访问失败: {e}")
        return False

def test_url_patterns():
    """
    测试不同的URL模式
    """
    print("=== 测试URL模式 ===")
    
    # 测试URL模式
    test_urls = [
        "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=221247981&classId=122555170&examId=6991302&cpi=356795813",
        "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId=221247981&classId=122555170&tId=6991302&id=160210345&p=1&tag=1&enc=ff2516cd4657243633c6a3f0eeb8c098&cpi=356795813&openc=687cf461734107a032056957d0b52d39&newMooc=true"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url[:80]}...")
        test_exam_url_access(url)

def analyze_url_differences():
    """
    分析URL差异
    """
    print("\n=== 分析URL差异 ===")
    
    # 当前生成的URL格式
    current_format = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={courseId}&classId={classId}&examId={examId}&cpi={cpi}&validate={validate}"
    
    # 期望的URL格式（从用户提供的信息）
    expected_format = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={courseId}&classId={classId}&tId={tId}&id={id}&p=1&tag=1&enc={enc}&cpi={cpi}&openc={openc}&newMooc=true"
    
    print(f"当前格式: {current_format}")
    print(f"期望格式: {expected_format}")
    
    print("\n关键差异:")
    print("1. examId vs tId - 参数名不同")
    print("2. 缺少 id, p, tag, enc, openc, newMooc 参数")
    print("3. 这些参数可能是在验证码成功后动态生成的")

def main():
    """
    主测试函数
    """
    print("=== 考试流程测试 ===")
    
    # 测试URL模式
    test_url_patterns()
    
    # 分析URL差异
    analyze_url_differences()
    
    print("\n=== 测试完成 ===")
    print("建议:")
    print("1. 验证码成功后需要实际请求页面获取完整参数")
    print("2. 可能需要解析页面内容或API响应来获取 id, enc, openc 等参数")
    print("3. 考虑添加重试机制和更详细的错误处理")

if __name__ == "__main__":
    main()
