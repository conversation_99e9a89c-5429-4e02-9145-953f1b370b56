"""
简化的超星学习通登录管理器
不依赖外部模块，提供基本的登录功能
"""
import requests
import time
import re
import json
import urllib3
from urllib.parse import urlencode

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleLoginManager:
    """简化的学习通登录管理器"""
    
    def __init__(self, username, password):
        """
        初始化登录管理器
        
        Args:
            username: 学习通用户名
            password: 学习通密码
        """
        self.username = username
        self.password = password
        self.session = requests.Session()

        # 禁用SSL验证和配置适配器
        self.session.verify = False

        # 配置HTTP适配器以处理SSL问题
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置基本请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.logged_in = False
        
    def login(self):
        """
        执行登录操作
        
        Returns:
            bool: 登录是否成功
        """
        try:
            print(f"正在登录用户: {self.username}")
            
            # 方法1: 尝试直接登录（模拟最简单的登录流程）
            if self._try_simple_login():
                self.logged_in = True
                print("登录成功！")
                return True
            
            # 方法2: 如果简单登录失败，尝试更完整的登录流程
            if self._try_full_login():
                self.logged_in = True
                print("登录成功！")
                return True
            
            print("登录失败")
            return False
            
        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False
    
    def _try_simple_login(self):
        """
        尝试简单登录方式
        """
        try:
            # 访问登录页面
            login_url = "https://passport2.chaoxing.com/api/login"
            
            # 构建登录数据
            login_data = {
                'uname': self.username,
                'password': self.password,
                'fid': '-1',
                'refer': 'https://mooc1.chaoxing.com',
                'forbidotherlogin': '0'
            }
            
            # 发送登录请求
            response = self.session.post(login_url, data=login_data, timeout=30, verify=False)
            
            if response.status_code == 200:
                # 检查登录结果
                if self._check_login_success(response):
                    return True
            
            return False
            
        except Exception as e:
            print(f"简单登录失败: {e}")
            return False
    
    def _try_full_login(self):
        """
        尝试完整的登录流程
        """
        try:
            # 1. 访问主页获取初始cookies
            self.session.get("https://www.chaoxing.com", timeout=30, verify=False)

            # 2. 访问登录页面
            login_page_url = "https://passport2.chaoxing.com/login"
            self.session.get(login_page_url, timeout=30, verify=False)
            
            # 3. 执行登录
            login_api_url = "https://passport2.chaoxing.com/fanyalogin"
            
            login_data = {
                'uname': self.username,
                'password': self.password,
                'fid': '-1',
                'refer': 'https://mooc1.chaoxing.com',
                'forbidotherlogin': '0',
                't': 'true'
            }
            
            response = self.session.post(login_api_url, data=login_data, timeout=30, verify=False)
            
            if response.status_code == 200:
                if self._check_login_success(response):
                    return True
            
            return False
            
        except Exception as e:
            print(f"完整登录失败: {e}")
            return False
    
    def _check_login_success(self, response):
        """
        检查登录是否成功
        
        Args:
            response: 登录响应
            
        Returns:
            bool: 是否登录成功
        """
        try:
            # 检查响应内容
            response_text = response.text
            
            # 方法1: 检查JSON响应
            try:
                result = response.json()
                if result.get('status') == True or result.get('result') == 1:
                    return True
            except:
                pass
            
            # 方法2: 检查响应文本
            success_indicators = [
                '"status":true',
                '"result":1',
                'success',
                '登录成功'
            ]
            
            for indicator in success_indicators:
                if indicator in response_text:
                    return True
            
            # 方法3: 检查是否有重定向到主页
            if response.url and 'chaoxing.com' in response.url:
                return True
            
            # 方法4: 检查cookies中是否有登录标识
            for cookie in self.session.cookies:
                if cookie.name in ['_uid', 'UID', 'fid'] and cookie.value:
                    return True
            
            return False
            
        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False
    
    def is_logged_in(self):
        """
        检查是否已登录
        
        Returns:
            bool: 是否已登录
        """
        return self.logged_in
    
    def get_session(self):
        """
        获取会话对象
        
        Returns:
            requests.Session: 会话对象
        """
        return self.session
    
    def test_login_status(self):
        """
        测试登录状态
        """
        try:
            # 访问需要登录的页面来测试
            test_url = "https://mooc1.chaoxing.com/space/index"
            response = self.session.get(test_url, timeout=30, verify=False)
            
            if response.status_code == 200:
                # 检查页面内容是否包含用户信息
                if self.username in response.text or '个人空间' in response.text:
                    print("登录状态验证成功")
                    return True
            
            print("登录状态验证失败")
            return False
            
        except Exception as e:
            print(f"测试登录状态失败: {e}")
            return False

# 测试代码
if __name__ == "__main__":
    # 测试登录管理器
    username = "test_user"
    password = "test_password"
    
    login_manager = SimpleLoginManager(username, password)
    
    if login_manager.login():
        print("登录测试成功")
        login_manager.test_login_status()
    else:
        print("登录测试失败")
