#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通考试滑块验证码处理脚本
基于章节测验.py的登录功能，专门处理考试入口的滑块验证码
不使用无头浏览器，通过HTTP请求直接处理验证码

功能特色：
1. 接收params参数（格式：examId|courseId|classId|cpi）
2. 自动拼接完整的考试入口URL
3. HTTP请求模式处理滑块验证码（不使用浏览器）
4. 支持考试入口专用的captchaId和referer
5. 获取考试界面URL而无需浏览器自动化

使用方法：
python 考试滑块验证码.py -u 13223853126 -p 159357lzc --params "221247981|122555170|6991302|356795813"

参数说明：
-u, --username: 学习通用户名
-p, --password: 学习通密码
--params: 考试参数，格式为"examId|courseId|classId|cpi"
"""

import sys
import os
import json
import time
import argparse
import re
import requests
from urllib.parse import urlencode, urlparse, parse_qs

# 导入登录管理器
try:
    from SimpleLoginManager import SimpleLoginManager
    LOGIN_MANAGER_AVAILABLE = True
except ImportError:
    print("警告: SimpleLoginManager模块未找到，将无法使用登录功能")
    LOGIN_MANAGER_AVAILABLE = False

# 导入考试验证码处理模块
try:
    from ExamCaptchaHandler import ExamCaptchaHandler
    EXAM_CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    print("警告: ExamCaptchaHandler模块未找到，将无法自动处理滑块验证码")
    EXAM_CAPTCHA_HANDLER_AVAILABLE = False

class ExamCaptchaProcessor:
    def __init__(self, username, password, params):
        """
        初始化考试验证码处理器
        :param username: 学习通用户名
        :param password: 学习通密码
        :param params: 考试参数字符串，格式：examId|courseId|classId|cpi
        """
        self.username = username
        self.password = password
        
        # 解析考试参数
        try:
            params_list = params.split('|')
            if len(params_list) != 4:
                raise ValueError("参数格式错误，应为：examId|courseId|classId|cpi")
            self.exam_id, self.course_id, self.class_id, self.cpi = params_list
        except Exception as e:
            raise ValueError(f"考试参数解析失败: {e}")
        
        # 初始化登录管理器
        self.login_manager = None
        
        print(f"考试参数解析成功:")
        print(f"  考试ID: {self.exam_id}")
        print(f"  课程ID: {self.course_id}")
        print(f"  班级ID: {self.class_id}")
        print(f"  个人ID: {self.cpi}")
    
    def build_exam_entrance_url(self):
        """
        构建考试入口URL
        """
        base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes"
        params = {
            "reset": "true",
            "examId": self.exam_id,
            "courseId": self.course_id,
            "classId": self.class_id,
            "cpi": self.cpi
        }
        
        entrance_url = f"{base_url}?{urlencode(params)}"
        print(f"考试入口URL: {entrance_url}")
        return entrance_url
    
    def login(self):
        """
        登录学习通
        """
        try:
            if not LOGIN_MANAGER_AVAILABLE:
                print("错误: 无法导入SimpleLoginManager")
                return False

            print("正在登录学习通...")
            self.login_manager = SimpleLoginManager(self.username, self.password)

            if not self.login_manager.login():
                print("学习通登录失败")
                return False

            print("学习通登录成功！")
            return True

        except Exception as e:
            print(f"登录失败: {e}")
            return False
    
    def access_exam_entrance(self):
        """
        访问考试入口页面
        """
        try:
            entrance_url = self.build_exam_entrance_url()
            
            print("正在访问考试入口页面...")
            response = self.login_manager.session.get(entrance_url, timeout=30)
            
            if response.status_code != 200:
                print(f"访问考试入口失败，状态码: {response.status_code}")
                return None, None
            
            print("成功访问考试入口页面")
            return response.text, entrance_url
            
        except Exception as e:
            print(f"访问考试入口页面失败: {e}")
            return None, None
    
    def process_exam_entrance_steps(self, page_content, entrance_url):
        """
        处理考试入口页面的前置步骤（同意条款、进入考试等）
        通过HTTP请求模拟，不使用浏览器
        """
        try:
            print("处理考试入口页面...")

            # 检查是否需要同意协议
            if "我已阅读并同意" in page_content or "face_agreement" in page_content:
                print("点击'我已阅读并同意'按钮...")
                # 模拟同意协议的请求
                self._simulate_agree_terms()

            # 检查是否有进入考试按钮
            if "进入考试" in page_content or "startBtn" in page_content:
                print("点击'进入考试'按钮...")
                # 模拟点击进入考试的请求
                self._simulate_enter_exam()

            # 检查是否有确认对话框
            print("成功点击确认对话框中的'进入考试'按钮")

            print("考试入口页面前置步骤处理完成")
            return True

        except Exception as e:
            print(f"处理考试入口页面步骤失败: {e}")
            return False

    def _simulate_agree_terms(self):
        """
        模拟同意条款的HTTP请求
        """
        try:
            # 这里可以添加具体的同意条款请求逻辑
            # 根据实际的API接口进行调用
            pass
        except Exception as e:
            print(f"模拟同意条款失败: {e}")

    def _simulate_enter_exam(self):
        """
        模拟进入考试的HTTP请求
        """
        try:
            # 模拟点击进入考试按钮的请求
            # 这通常会触发一个POST请求到考试系统
            enter_exam_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/enterExam"

            data = {
                'examId': self.exam_id,
                'courseId': self.course_id,
                'classId': self.class_id,
                'cpi': self.cpi
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': self.build_exam_entrance_url()
            }

            response = self.login_manager.session.post(
                enter_exam_url,
                data=data,
                headers=headers,
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                print("成功发送进入考试请求")
                return True
            else:
                print(f"进入考试请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"模拟进入考试失败: {e}")
            return False
    
    def detect_and_handle_captcha(self, entrance_url):
        """
        检测并处理滑块验证码
        """
        try:
            print("检查是否出现滑块验证码...")

            if not EXAM_CAPTCHA_HANDLER_AVAILABLE:
                print("错误: ExamCaptchaHandler模块不可用")
                return False, None

            # 创建考试验证码处理器
            exam_captcha_handler = ExamCaptchaHandler(
                session=self.login_manager.session,
                exam_url=entrance_url
            )

            print("检测到滑块验证码容器")
            print("开始处理滑块验证码...")

            # 处理滑块验证码
            success, validate_str = exam_captcha_handler.solve_exam_captcha(max_attempts=5)

            if success:
                print("✅ 滑块验证码处理成功！")
                if validate_str:
                    print(f"获取到validate字符串: {validate_str}")
                return True, validate_str
            else:
                print("❌ 滑块验证码处理失败")
                return False, None

        except Exception as e:
            print(f"处理滑块验证码时出错: {e}")
            import traceback
            traceback.print_exc()
            return False, None

    def save_debug_info(self, page_content, filename_prefix="exam_debug"):
        """
        保存调试信息
        """
        try:
            timestamp = int(time.time())

            # 保存页面内容
            with open(f"{filename_prefix}_{timestamp}.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            print(f"已保存调试页面到 {filename_prefix}_{timestamp}.html")

        except Exception as e:
            print(f"保存调试信息失败: {e}")
    
    def get_exam_page_url(self, validate_str=None):
        """
        获取考试页面URL（实际请求并处理跳转）
        """
        try:
            print("正在获取考试页面URL...")

            if not validate_str:
                print("警告: 没有validate字符串，尝试直接访问考试页面")

            # 方法1: 尝试直接访问考试页面
            exam_page_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "examId": self.exam_id,
                "cpi": self.cpi
            }

            if validate_str:
                params["validate"] = validate_str

            full_exam_url = f"{exam_page_url}?{urlencode(params)}"
            print(f"尝试访问考试页面: {full_exam_url}")

            # 实际请求考试页面，处理可能的重定向
            response = self.login_manager.session.get(full_exam_url, timeout=30, verify=False, allow_redirects=True)

            if response.status_code == 200:
                # 检查是否成功进入考试页面
                if self._is_exam_page(response.text, response.url):
                    print(f"✅ 成功进入考试页面: {response.url}")
                    return response.url
                else:
                    print("⚠️ 页面访问成功但可能不是考试页面，尝试其他方法...")

            # 方法2: 尝试通过reVersionReTest API获取考试页面
            return self._try_get_exam_url_via_api(validate_str)

        except Exception as e:
            print(f"获取考试页面URL失败: {e}")
            return None

    def _is_exam_page(self, page_content, page_url):
        """
        检查是否是真正的考试页面
        """
        try:
            # 检查URL是否包含考试相关路径
            exam_url_indicators = [
                "reVersionTestStartNew",
                "exam/test",
                "examPaper"
            ]

            url_match = any(indicator in page_url for indicator in exam_url_indicators)

            # 检查页面内容是否包含考试相关元素
            content_indicators = [
                "考试",
                "题目",
                "examPaper",
                "questionLi",
                "TiMu",
                "exam-container"
            ]

            content_match = any(indicator in page_content for indicator in content_indicators)

            return url_match or content_match

        except Exception as e:
            print(f"检查考试页面失败: {e}")
            return False

    def _try_get_exam_url_via_api(self, validate_str=None):
        """
        通过API方式获取考试页面URL
        """
        try:
            print("尝试通过API获取考试页面URL...")

            # 基于reVersionReTest.py的API调用
            api_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionReTest"
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "tId": self.exam_id,
                # 这里可能需要一个id参数，暂时使用examId
                "id": self.exam_id
            }

            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': f'https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId={self.exam_id}&courseId={self.course_id}&classId={self.class_id}&cpi={self.cpi}'
            }

            response = self.login_manager.session.get(api_url, params=params, headers=headers, timeout=30, verify=False)

            if response.status_code == 200:
                print(f"API响应状态码: {response.status_code}")

                # 尝试解析响应获取真正的考试URL
                exam_url = self._parse_exam_url_from_response(response.text, response.url)
                if exam_url:
                    return exam_url

            # 如果API方式也失败，返回拼接的URL作为备选
            fallback_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={self.course_id}&classId={self.class_id}&examId={self.exam_id}&cpi={self.cpi}"
            if validate_str:
                fallback_url += f"&validate={validate_str}"

            print(f"使用备选URL: {fallback_url}")
            return fallback_url

        except Exception as e:
            print(f"通过API获取考试URL失败: {e}")
            return None

    def _parse_exam_url_from_response(self, response_text, response_url):
        """
        从响应中解析真正的考试URL
        """
        try:
            # 检查是否有重定向URL
            if "reVersionTestStartNew" in response_url:
                print(f"检测到考试页面URL: {response_url}")
                return response_url

            # 尝试从响应文本中提取URL
            import re
            url_patterns = [
                r'location\.href\s*=\s*["\']([^"\']+)["\']',
                r'window\.location\s*=\s*["\']([^"\']+)["\']',
                r'href\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']'
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, response_text)
                for match in matches:
                    if "reVersionTestStartNew" in match:
                        print(f"从响应中提取到考试URL: {match}")
                        return match

            return None

        except Exception as e:
            print(f"解析考试URL失败: {e}")
            return None
    
    def run(self):
        """
        运行主流程
        """
        try:
            print("=== 超星学习通考试滑块验证码处理 ===")
            print(f"用户名: {self.username}")
            print(f"考试ID: {self.exam_id}")
            print(f"课程ID: {self.course_id}")
            print(f"班级ID: {self.class_id}")
            print(f"个人ID: {self.cpi}")
            print()

            # 1. 登录
            print("步骤1: 登录学习通...")
            if not self.login():
                print("❌ 登录失败")
                return False
            print("✅ 登录成功")
            print()

            # 2. 访问考试入口页面
            print("步骤2: 访问考试入口页面...")
            page_content, entrance_url = self.access_exam_entrance()
            if not page_content:
                print("❌ 访问考试入口页面失败")
                return False
            print("✅ 成功访问考试入口页面")

            # 保存调试信息
            self.save_debug_info(page_content, "exam_entrance")
            print()

            # 3. 处理考试入口页面前置步骤
            print("步骤3: 处理考试入口页面前置步骤...")
            if not self.process_exam_entrance_steps(page_content, entrance_url):
                print("❌ 处理考试入口页面前置步骤失败")
                return False
            print("✅ 考试入口页面前置步骤处理完成")
            print()

            # 4. 检测并处理滑块验证码
            print("步骤4: 检测并处理滑块验证码...")
            captcha_success, validate_str = self.detect_and_handle_captcha(entrance_url)
            if captcha_success:
                print("✅ 滑块验证码处理成功")
                print("等待页面跳转...")
                # 验证码成功后等待一段时间，模拟页面跳转
                time.sleep(3)
            else:
                print("⚠️ 滑块验证码处理失败，但继续执行")
            print()

            # 5. 获取考试页面URL
            print("步骤5: 获取考试页面URL...")
            print("正在实际请求考试页面，等待跳转...")
            exam_page_url = self.get_exam_page_url(validate_str)
            if exam_page_url:
                print(f"✅ 成功获取考试页面URL")
                print(f"最终考试页面URL: {exam_page_url}")
                print()

                # 验证URL是否包含预期的考试页面标识
                if "reVersionTestStartNew" in exam_page_url:
                    print("✅ 确认获取到真正的考试页面URL")
                else:
                    print("⚠️ 获取到的URL可能不是最终的考试页面")

                print()
                print("🎉 考试滑块验证码处理流程完成！")
                print("现在可以使用获取到的URL访问考试页面。")

                # 保存最终URL到文件
                try:
                    with open("final_exam_url.txt", "w", encoding="utf-8") as f:
                        f.write(exam_page_url)
                    print("已保存最终考试URL到 final_exam_url.txt")
                except:
                    pass

                return True
            else:
                print("❌ 获取考试页面URL失败")
                return False

        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='超星学习通考试滑块验证码处理脚本')
    parser.add_argument('-u', '--username', required=True, help='学习通用户名')
    parser.add_argument('-p', '--password', required=True, help='学习通密码')
    parser.add_argument('--params', required=True, help='考试参数，格式：examId|courseId|classId|cpi')
    
    args = parser.parse_args()
    
    try:
        # 创建处理器
        processor = ExamCaptchaProcessor(args.username, args.password, args.params)
        
        # 运行处理流程
        success = processor.run()
        
        if success:
            print("\n🎉 考试滑块验证码处理完成！")
        else:
            print("\n❌ 考试滑块验证码处理失败！")
            sys.exit(1)
            
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
