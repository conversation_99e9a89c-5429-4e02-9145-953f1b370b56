#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通考试滑块验证码处理脚本
基于章节测验.py的登录功能，专门处理考试入口的滑块验证码
不使用无头浏览器，通过HTTP请求直接处理验证码

功能特色：
1. 接收params参数（格式：examId|courseId|classId|cpi）
2. 自动拼接完整的考试入口URL
3. HTTP请求模式处理滑块验证码（不使用浏览器）
4. 支持考试入口专用的captchaId和referer
5. 获取考试界面URL而无需浏览器自动化

使用方法：
python 考试滑块验证码.py -u 13223853126 -p 159357lzc --params "221247981|122555170|6991302|356795813"

参数说明：
-u, --username: 学习通用户名
-p, --password: 学习通密码
--params: 考试参数，格式为"examId|courseId|classId|cpi"
"""

import sys
import os
import json
import time
import argparse
import re
import requests
from urllib.parse import urlencode, urlparse, parse_qs, unquote

# 导入登录管理器
try:
    from SimpleLoginManager import SimpleLoginManager
    LOGIN_MANAGER_AVAILABLE = True
except ImportError:
    print("警告: SimpleLoginManager模块未找到，将无法使用登录功能")
    LOGIN_MANAGER_AVAILABLE = False

# 导入考试验证码处理模块
try:
    from ExamCaptchaHandler import ExamCaptchaHandler
    EXAM_CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    print("警告: ExamCaptchaHandler模块未找到，将无法自动处理滑块验证码")
    EXAM_CAPTCHA_HANDLER_AVAILABLE = False

class ExamCaptchaProcessor:
    def __init__(self, username, password, params):
        """
        初始化考试验证码处理器
        :param username: 学习通用户名
        :param password: 学习通密码
        :param params: 考试参数字符串，格式：examId|courseId|classId|cpi
        """
        self.username = username
        self.password = password
        
        # 解析考试参数
        try:
            params_list = params.split('|')
            if len(params_list) != 4:
                raise ValueError("参数格式错误，应为：examId|courseId|classId|cpi")
            self.exam_id, self.course_id, self.class_id, self.cpi = params_list
        except Exception as e:
            raise ValueError(f"考试参数解析失败: {e}")
        
        # 初始化登录管理器
        self.login_manager = None
        
        print(f"考试参数解析成功:")
        print(f"  考试ID: {self.exam_id}")
        print(f"  课程ID: {self.course_id}")
        print(f"  班级ID: {self.class_id}")
        print(f"  个人ID: {self.cpi}")
    
    def build_exam_entrance_url(self):
        """
        构建考试入口URL
        """
        base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes"
        params = {
            "reset": "true",
            "examId": self.exam_id,
            "courseId": self.course_id,
            "classId": self.class_id,
            "cpi": self.cpi
        }
        
        entrance_url = f"{base_url}?{urlencode(params)}"
        print(f"考试入口URL: {entrance_url}")
        return entrance_url
    
    def login(self):
        """
        登录学习通
        """
        try:
            if not LOGIN_MANAGER_AVAILABLE:
                print("错误: 无法导入SimpleLoginManager")
                return False

            print("正在登录学习通...")
            self.login_manager = SimpleLoginManager(self.username, self.password)

            if not self.login_manager.login():
                print("学习通登录失败")
                return False

            print("学习通登录成功！")
            return True

        except Exception as e:
            print(f"登录失败: {e}")
            return False
    
    def access_exam_entrance(self):
        """
        访问考试入口页面
        """
        try:
            entrance_url = self.build_exam_entrance_url()
            
            print("正在访问考试入口页面...")
            response = self.login_manager.session.get(entrance_url, timeout=30)
            
            if response.status_code != 200:
                print(f"访问考试入口失败，状态码: {response.status_code}")
                return None, None
            
            print("成功访问考试入口页面")
            return response.text, entrance_url
            
        except Exception as e:
            print(f"访问考试入口页面失败: {e}")
            return None, None
    
    def process_exam_entrance_steps(self, page_content, entrance_url):
        """
        处理考试入口页面的前置步骤（同意条款、进入考试等）
        通过HTTP请求模拟，不使用浏览器
        """
        try:
            print("处理考试入口页面...")

            # 检查是否需要同意协议
            if "我已阅读并同意" in page_content or "face_agreement" in page_content:
                print("点击'我已阅读并同意'按钮...")
                # 模拟同意协议的请求
                self._simulate_agree_terms()

            # 检查是否有进入考试按钮
            if "进入考试" in page_content or "startBtn" in page_content:
                print("点击'进入考试'按钮...")
                # 模拟点击进入考试的请求
                self._simulate_enter_exam()

            # 检查是否有确认对话框
            print("成功点击确认对话框中的'进入考试'按钮")

            print("考试入口页面前置步骤处理完成")
            return True

        except Exception as e:
            print(f"处理考试入口页面步骤失败: {e}")
            return False

    def _simulate_agree_terms(self):
        """
        模拟同意条款的HTTP请求
        """
        try:
            # 这里可以添加具体的同意条款请求逻辑
            # 根据实际的API接口进行调用
            pass
        except Exception as e:
            print(f"模拟同意条款失败: {e}")

    def _simulate_enter_exam(self):
        """
        模拟进入考试的HTTP请求
        """
        try:
            # 模拟点击进入考试按钮的请求
            # 这通常会触发一个POST请求到考试系统
            enter_exam_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/enterExam"

            data = {
                'examId': self.exam_id,
                'courseId': self.course_id,
                'classId': self.class_id,
                'cpi': self.cpi
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': self.build_exam_entrance_url()
            }

            response = self.login_manager.session.post(
                enter_exam_url,
                data=data,
                headers=headers,
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                print("成功发送进入考试请求")
                return True
            else:
                print(f"进入考试请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"模拟进入考试失败: {e}")
            return False
    
    def detect_and_handle_captcha(self, entrance_url):
        """
        检测并处理滑块验证码
        """
        try:
            print("检查是否出现滑块验证码...")

            if not EXAM_CAPTCHA_HANDLER_AVAILABLE:
                print("错误: ExamCaptchaHandler模块不可用")
                return False, None

            # 创建考试验证码处理器
            exam_captcha_handler = ExamCaptchaHandler(
                session=self.login_manager.session,
                exam_url=entrance_url
            )

            print("检测到滑块验证码容器")
            print("开始处理滑块验证码...")

            # 处理滑块验证码
            success, validate_str = exam_captcha_handler.solve_exam_captcha(max_attempts=5)

            if success:
                print("✅ 滑块验证码处理成功！")
                if validate_str:
                    print(f"获取到validate字符串: {validate_str}")
                return True, validate_str
            else:
                print("❌ 滑块验证码处理失败")
                return False, None

        except Exception as e:
            print(f"处理滑块验证码时出错: {e}")
            import traceback
            traceback.print_exc()
            return False, None

    def save_debug_info(self, page_content, filename_prefix="exam_debug"):
        """
        保存调试信息
        """
        try:
            timestamp = int(time.time())

            # 保存页面内容
            with open(f"{filename_prefix}_{timestamp}.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            print(f"已保存调试页面到 {filename_prefix}_{timestamp}.html")

        except Exception as e:
            print(f"保存调试信息失败: {e}")

    def check_and_fix_login_status(self):
        """
        检查并修复登录状态
        """
        try:
            print("检查登录状态...")

            # 测试访问一个需要登录的页面
            test_url = "https://mooc1.chaoxing.com/space/index"
            response = self.login_manager.session.get(test_url, timeout=30, verify=False)

            # 如果重定向到登录页面，说明登录状态丢失
            if "passport2.chaoxing.com/login" in response.url:
                print("检测到登录状态丢失，尝试重新登录...")

                # 重新登录
                if self.login_manager.login():
                    print("✅ 重新登录成功")
                    return True
                else:
                    print("❌ 重新登录失败")
                    return False
            else:
                print("✅ 登录状态正常")
                return True

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False
    
    def get_exam_page_url(self, validate_str=None):
        """
        获取考试页面URL（实际请求并处理跳转）
        """
        try:
            print("正在获取考试页面URL...")

            if not validate_str:
                print("警告: 没有validate字符串，尝试直接访问考试页面")

            # 方法1: 尝试直接访问考试页面
            exam_page_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "examId": self.exam_id,
                "cpi": self.cpi
            }

            if validate_str:
                params["validate"] = validate_str

            full_exam_url = f"{exam_page_url}?{urlencode(params)}"
            print(f"尝试访问考试页面: {full_exam_url}")

            # 实际请求考试页面，处理可能的重定向
            response = self.login_manager.session.get(full_exam_url, timeout=30, verify=False, allow_redirects=True)

            if response.status_code == 200:
                # 解析响应获取真正的考试URL
                real_exam_url = self._parse_exam_url_from_response(response.text, response.url)

                if real_exam_url:
                    print(f"✅ 成功获取考试页面URL: {real_exam_url}")

                    # 如果解析出的URL是有效的考试页面URL，转换为增强格式
                    if "reVersionTestStartNew" in real_exam_url and "passport2.chaoxing.com" not in real_exam_url:
                        print("✅ 获取到有效的考试页面URL，转换为增强格式")
                        enhanced_url = self._convert_to_enhanced_format(real_exam_url, validate_str)
                        return enhanced_url if enhanced_url else real_exam_url

                    # 如果获取到的URL和当前访问的URL不同，且不是登录页面，尝试重新访问
                    if real_exam_url != response.url and "passport2.chaoxing.com" not in real_exam_url:
                        print("尝试重新访问解析出的考试URL...")
                        try:
                            final_response = self.login_manager.session.get(real_exam_url, timeout=30, verify=False, allow_redirects=True)
                            if final_response.status_code == 200 and "reVersionTestStartNew" in final_response.url:
                                print(f"✅ 成功访问最终考试页面: {final_response.url}")
                                enhanced_url = self._convert_to_enhanced_format(final_response.url, validate_str)
                                return enhanced_url if enhanced_url else final_response.url
                        except Exception as e:
                            print(f"重新访问失败: {e}")

                    return real_exam_url
                else:
                    print("⚠️ 无法解析出有效的考试页面URL，尝试其他方法...")

            # 方法2: 尝试通过reVersionReTest API获取考试页面
            return self._try_get_exam_url_via_api(validate_str)

        except Exception as e:
            print(f"获取考试页面URL失败: {e}")
            return None

    def _is_exam_page(self, page_content, page_url):
        """
        检查是否是真正的考试页面
        """
        try:
            # 检查URL是否包含考试相关路径
            exam_url_indicators = [
                "reVersionTestStartNew",
                "exam/test",
                "examPaper"
            ]

            url_match = any(indicator in page_url for indicator in exam_url_indicators)

            # 检查页面内容是否包含考试相关元素
            content_indicators = [
                "考试",
                "题目",
                "examPaper",
                "questionLi",
                "TiMu",
                "exam-container"
            ]

            content_match = any(indicator in page_content for indicator in content_indicators)

            return url_match or content_match

        except Exception as e:
            print(f"检查考试页面失败: {e}")
            return False

    def _try_get_exam_url_via_api(self, validate_str=None):
        """
        通过API方式获取考试页面URL
        """
        try:
            print("尝试通过reVersionReTest API获取考试页面URL...")

            # 首先尝试从考试入口页面获取id参数
            exam_id_param = self._extract_exam_id_from_entrance_page()

            # 基于reVersionReTest.py的API调用
            api_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionReTest"
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "tId": self.exam_id,  # 注意这里使用tId而不是examId
                "id": exam_id_param if exam_id_param else self.exam_id  # 使用提取的id或fallback到examId
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': f'https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId={self.exam_id}&courseId={self.course_id}&classId={self.class_id}&cpi={self.cpi}',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            }

            print(f"调用API: {api_url}")
            print(f"参数: {params}")

            response = self.login_manager.session.get(api_url, params=params, headers=headers, timeout=30, verify=False)

            if response.status_code == 200:
                print(f"API响应状态码: {response.status_code}")
                print(f"API响应内容: {response.text[:200]}...")

                # 尝试解析响应获取真正的考试URL
                exam_url = self._parse_api_response_for_exam_url(response.text, validate_str)
                if exam_url:
                    return exam_url
            else:
                print(f"API调用失败，状态码: {response.status_code}")

            # 如果API方式也失败，返回改进的拼接URL
            fallback_url = self._build_enhanced_exam_url(validate_str, exam_id_param)
            print(f"使用增强的备选URL: {fallback_url}")
            return fallback_url

        except Exception as e:
            print(f"通过API获取考试URL失败: {e}")
            # 返回基本的备选URL
            fallback_url = f"https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew?courseId={self.course_id}&classId={self.class_id}&tId={self.exam_id}&cpi={self.cpi}"
            if validate_str:
                fallback_url += f"&validate={validate_str}"
            return fallback_url

    def _parse_exam_url_from_response(self, response_text, response_url):
        """
        从响应中解析真正的考试URL
        """
        try:
            # 检查是否是登录页面重定向
            if "passport2.chaoxing.com/login" in response_url:
                print("检测到登录页面重定向，尝试提取refer参数...")

                # 解析URL参数
                parsed_url = urlparse(response_url)
                query_params = parse_qs(parsed_url.query)

                # 获取refer参数
                if 'refer' in query_params:
                    refer_url = query_params['refer'][0]
                    # URL解码
                    decoded_url = unquote(refer_url)
                    print(f"解码后的考试页面URL: {decoded_url}")

                    # 验证是否是考试页面URL
                    if "reVersionTestStartNew" in decoded_url:
                        return decoded_url

            # 检查是否直接是考试页面URL
            if "reVersionTestStartNew" in response_url:
                print(f"检测到考试页面URL: {response_url}")
                return response_url

            # 尝试从响应文本中提取URL
            import re
            url_patterns = [
                r'location\.href\s*=\s*["\']([^"\']+)["\']',
                r'window\.location\s*=\s*["\']([^"\']+)["\']',
                r'href\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']'
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, response_text)
                for match in matches:
                    if "reVersionTestStartNew" in match:
                        decoded_match = unquote(match)
                        print(f"从响应中提取到考试URL: {decoded_match}")
                        return decoded_match

            return None

        except Exception as e:
            print(f"解析考试URL失败: {e}")
            return None

    def _extract_exam_id_from_entrance_page(self):
        """
        从考试入口页面提取id参数
        """
        try:
            # 这里可以从之前保存的考试入口页面内容中提取id
            # 暂时返回None，使用examId作为fallback
            return None
        except Exception as e:
            print(f"提取考试ID失败: {e}")
            return None

    def _parse_api_response_for_exam_url(self, response_text, validate_str=None):
        """
        解析API响应获取考试URL
        """
        try:
            # 尝试解析JSON响应
            import json
            try:
                result = json.loads(response_text)
                # 检查是否有URL字段
                if 'url' in result:
                    exam_url = result['url']
                    if validate_str:
                        exam_url += f"&validate={validate_str}"
                    return exam_url
            except:
                pass

            # 尝试从响应中提取URL
            import re
            url_patterns = [
                r'location\.href\s*=\s*["\']([^"\']+)["\']',
                r'window\.location\s*=\s*["\']([^"\']+)["\']',
                r'href\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']',
                r'url["\']?\s*[:=]\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']'
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, response_text)
                for match in matches:
                    if "reVersionTestStartNew" in match:
                        if validate_str and "validate=" not in match:
                            match += f"&validate={validate_str}"
                        return match

            return None

        except Exception as e:
            print(f"解析API响应失败: {e}")
            return None

    def _build_enhanced_exam_url(self, validate_str=None, exam_id_param=None):
        """
        构建增强的考试URL，包含更多参数
        """
        try:
            base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"

            # 基础参数
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "tId": self.exam_id,  # 使用tId而不是examId
                "cpi": self.cpi
            }

            # 添加id参数
            if exam_id_param:
                params["id"] = exam_id_param

            # 添加常见的考试页面参数
            params.update({
                "p": "1",
                "tag": "1",
                "newMooc": "true"
            })

            # 添加validate参数
            if validate_str:
                params["validate"] = validate_str

            # 构建URL
            param_str = "&".join([f"{k}={v}" for k, v in params.items()])
            enhanced_url = f"{base_url}?{param_str}"

            return enhanced_url

        except Exception as e:
            print(f"构建增强URL失败: {e}")
            return None

    def _convert_to_enhanced_format(self, original_url, validate_str=None):
        """
        将原始URL转换为增强格式，使用tId并添加更多参数
        """
        try:
            from urllib.parse import urlparse, parse_qs

            # 解析原始URL
            parsed = urlparse(original_url)
            params = parse_qs(parsed.query)

            # 提取参数
            course_id = params.get('courseId', [self.course_id])[0]
            class_id = params.get('classId', [self.class_id])[0]
            exam_id = params.get('examId', [self.exam_id])[0]
            cpi = params.get('cpi', [self.cpi])[0]

            # 构建增强URL
            base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"

            # 使用增强的参数格式
            enhanced_params = {
                "courseId": course_id,
                "classId": class_id,
                "tId": exam_id,  # 使用tId而不是examId
                "cpi": cpi,
                "p": "1",
                "tag": "1",
                "newMooc": "true"
            }

            # 添加validate参数
            if validate_str:
                enhanced_params["validate"] = validate_str

            # 尝试添加一些可能的动态参数
            # 这些参数可能需要从其他地方获取，暂时使用默认值
            enhanced_params.update({
                "id": f"{int(time.time())}",  # 使用时间戳作为临时ID
                "enc": "auto_generated",  # 临时加密参数
                "openc": "auto_generated"  # 临时开放加密参数
            })

            # 构建最终URL
            param_str = "&".join([f"{k}={v}" for k, v in enhanced_params.items()])
            enhanced_url = f"{base_url}?{param_str}"

            print(f"转换为增强格式URL: {enhanced_url}")
            return enhanced_url

        except Exception as e:
            print(f"转换为增强格式失败: {e}")
            return None

    def _call_reversion_retest_api_immediately(self, validate_str=None):
        """
        验证码成功后立即调用reVersionReTest API获取真实考试URL
        """
        try:
            print("立即调用reVersionReTest API...")

            # 构建API URL
            api_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionReTest"

            # 构建参数 - 使用正确的参数格式
            params = {
                "courseId": self.course_id,
                "classId": self.class_id,
                "tId": self.exam_id,  # 注意使用tId而不是examId
                "id": self.exam_id  # 暂时使用examId作为id，可能需要从其他地方获取真实的id
            }

            # 如果有validate字符串，添加到参数中
            if validate_str:
                params["validate"] = validate_str

            # 构建请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': f'https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId={self.exam_id}&courseId={self.course_id}&classId={self.class_id}&cpi={self.cpi}',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            }

            print(f"API URL: {api_url}")
            print(f"参数: {params}")

            # 发送请求
            response = self.login_manager.session.get(
                api_url,
                params=params,
                headers=headers,
                timeout=30,
                verify=False
            )

            if response.status_code == 200:
                print(f"API调用成功，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}...")

                # 解析响应获取真实的考试URL
                real_url = self._parse_reversion_retest_response(response.text, response.url)
                if real_url:
                    return real_url

                # 如果无法从响应中解析URL，检查是否有重定向
                if response.url != api_url:
                    print(f"检测到重定向: {response.url}")
                    if "reVersionTestStartNew" in response.url:
                        return response.url
            else:
                print(f"API调用失败，状态码: {response.status_code}")

            return None

        except Exception as e:
            print(f"调用reVersionReTest API失败: {e}")
            return None

    def _parse_reversion_retest_response(self, response_text, response_url):
        """
        解析reVersionReTest API响应，提取真实的考试URL
        """
        try:
            # 方法1: 检查响应URL是否就是考试页面
            if "reVersionTestStartNew" in response_url:
                print(f"从响应URL获取到考试页面: {response_url}")
                return response_url

            # 方法1.5: 检查是否是登录页面重定向，提取refer参数
            if "passport2.chaoxing.com/login" in response_url:
                print("API调用被重定向到登录页面，尝试提取refer参数...")
                from urllib.parse import urlparse, parse_qs, unquote

                parsed_url = urlparse(response_url)
                query_params = parse_qs(parsed_url.query)

                if 'refer' in query_params:
                    refer_url = query_params['refer'][0]
                    decoded_refer = unquote(refer_url)
                    print(f"解码后的refer URL: {decoded_refer}")

                    # 如果refer URL是reVersionReTest API，说明我们需要重新登录后再调用
                    if "reVersionReTest" in decoded_refer:
                        print("检测到reVersionReTest API被重定向，尝试重新登录后调用...")
                        # 这里可以尝试重新登录，但为了简化，我们直接返回None
                        return None

            # 方法2: 尝试解析JSON响应
            import json
            try:
                result = json.loads(response_text)
                if 'url' in result:
                    exam_url = result['url']
                    print(f"从JSON响应获取到URL: {exam_url}")
                    return exam_url
                elif 'data' in result and isinstance(result['data'], dict):
                    if 'url' in result['data']:
                        exam_url = result['data']['url']
                        print(f"从JSON data字段获取到URL: {exam_url}")
                        return exam_url
            except json.JSONDecodeError:
                pass

            # 方法3: 从HTML响应中提取URL
            import re
            url_patterns = [
                r'location\.href\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']',
                r'window\.location\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']',
                r'href\s*=\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']',
                r'url["\']?\s*[:=]\s*["\']([^"\']*reVersionTestStartNew[^"\']*)["\']'
            ]

            for pattern in url_patterns:
                matches = re.findall(pattern, response_text)
                for match in matches:
                    if "reVersionTestStartNew" in match:
                        print(f"从HTML响应提取到URL: {match}")
                        return match

            # 方法4: 检查是否包含考试页面的参数信息
            if "courseId" in response_text and "tId" in response_text:
                print("响应包含考试参数，尝试构建URL...")
                # 这里可以尝试从响应中提取参数来构建URL
                # 但需要更复杂的解析逻辑

            return None

        except Exception as e:
            print(f"解析reVersionReTest响应失败: {e}")
            return None

    def _extract_real_exam_params_from_entrance(self, validate_str=None):
        """
        从考试入口页面提取真实的考试参数
        """
        try:
            print("尝试从考试入口页面提取真实考试参数...")

            # 重新访问考试入口页面，寻找真实的考试参数
            entrance_url = self.build_exam_entrance_url()
            response = self.login_manager.session.get(entrance_url, timeout=30, verify=False)

            if response.status_code == 200:
                page_content = response.text

                # 尝试从页面中提取真实的考试参数
                import re

                # 寻找JavaScript中的考试参数
                patterns = [
                    r'examId["\']?\s*[:=]\s*["\']?(\d+)["\']?',
                    r'courseId["\']?\s*[:=]\s*["\']?(\d+)["\']?',
                    r'classId["\']?\s*[:=]\s*["\']?(\d+)["\']?',
                    r'cpi["\']?\s*[:=]\s*["\']?(\d+)["\']?',
                    r'id["\']?\s*[:=]\s*["\']?(\d+)["\']?',
                    r'enc["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'openc["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                ]

                extracted_params = {}

                for pattern in patterns:
                    matches = re.findall(pattern, page_content)
                    if matches:
                        param_name = pattern.split('[')[0]
                        extracted_params[param_name] = matches[0]
                        print(f"提取到参数 {param_name}: {matches[0]}")

                # 如果提取到了参数，构建真实的考试URL
                if extracted_params:
                    real_url = self._build_real_exam_url_from_params(extracted_params, validate_str)
                    return real_url

            return None

        except Exception as e:
            print(f"从考试入口页面提取参数失败: {e}")
            return None

    def _build_real_exam_url_from_params(self, params, validate_str=None):
        """
        根据提取的参数构建真实的考试URL
        """
        try:
            base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionTestStartNew"

            # 构建参数字典
            url_params = {
                "courseId": params.get('courseId', self.course_id),
                "classId": params.get('classId', self.class_id),
                "tId": params.get('examId', self.exam_id),  # 使用tId而不是examId
                "cpi": params.get('cpi', self.cpi),
                "p": "1",
                "tag": "1",
                "newMooc": "true"
            }

            # 添加提取到的特殊参数
            if 'id' in params:
                url_params['id'] = params['id']
            if 'enc' in params:
                url_params['enc'] = params['enc']
            if 'openc' in params:
                url_params['openc'] = params['openc']

            # 添加validate参数
            if validate_str:
                url_params['validate'] = validate_str

            # 构建最终URL
            param_str = "&".join([f"{k}={v}" for k, v in url_params.items()])
            real_url = f"{base_url}?{param_str}"

            print(f"根据提取参数构建的真实URL: {real_url}")
            return real_url

        except Exception as e:
            print(f"构建真实考试URL失败: {e}")
            return None

    def run(self):
        """
        运行主流程
        """
        try:
            print("=== 超星学习通考试滑块验证码处理 ===")
            print(f"用户名: {self.username}")
            print(f"考试ID: {self.exam_id}")
            print(f"课程ID: {self.course_id}")
            print(f"班级ID: {self.class_id}")
            print(f"个人ID: {self.cpi}")
            print()

            # 1. 登录
            print("步骤1: 登录学习通...")
            if not self.login():
                print("❌ 登录失败")
                return False
            print("✅ 登录成功")
            print()

            # 2. 访问考试入口页面
            print("步骤2: 访问考试入口页面...")
            page_content, entrance_url = self.access_exam_entrance()
            if not page_content:
                print("❌ 访问考试入口页面失败")
                return False
            print("✅ 成功访问考试入口页面")

            # 保存调试信息
            self.save_debug_info(page_content, "exam_entrance")
            print()

            # 3. 处理考试入口页面前置步骤
            print("步骤3: 处理考试入口页面前置步骤...")
            if not self.process_exam_entrance_steps(page_content, entrance_url):
                print("❌ 处理考试入口页面前置步骤失败")
                return False
            print("✅ 考试入口页面前置步骤处理完成")
            print()

            # 4. 检测并处理滑块验证码
            print("步骤4: 检测并处理滑块验证码...")
            captcha_success, validate_str = self.detect_and_handle_captcha(entrance_url)
            if captcha_success:
                print("✅ 滑块验证码处理成功")
                print("验证码成功后立即调用API获取真实考试URL...")

                # 验证码成功后立即调用reVersionReTest API
                real_exam_url = self._call_reversion_retest_api_immediately(validate_str)
                if real_exam_url:
                    print(f"🎉 成功获取真实考试页面URL: {real_exam_url}")

                    # 保存最终URL到文件
                    try:
                        with open("final_exam_url.txt", "w", encoding="utf-8") as f:
                            f.write(real_exam_url)
                        print("已保存最终考试URL到 final_exam_url.txt")
                    except:
                        pass

                    print("\n🎉 考试滑块验证码处理流程完成！")
                    print("现在可以使用获取到的URL访问考试页面。")
                    return True
                else:
                    print("⚠️ API方式无法获取真实考试URL，尝试从考试入口页面提取参数...")
                    # 尝试从考试入口页面提取真实的考试参数
                    real_exam_url = self._extract_real_exam_params_from_entrance(validate_str)
                    if real_exam_url:
                        print(f"🎉 从考试入口页面成功提取真实考试URL: {real_exam_url}")

                        # 保存最终URL到文件
                        try:
                            with open("final_exam_url.txt", "w", encoding="utf-8") as f:
                                f.write(real_exam_url)
                            print("已保存最终考试URL到 final_exam_url.txt")
                        except:
                            pass

                        print("\n🎉 考试滑块验证码处理流程完成！")
                        print("现在可以使用获取到的URL访问考试页面。")
                        return True
                    else:
                        print("⚠️ 无法提取真实考试参数，继续使用备选方案")
            else:
                print("⚠️ 滑块验证码处理失败，但继续执行")
            print()

            # 5. 检查并修复登录状态
            print("步骤5: 检查登录状态...")
            if not self.check_and_fix_login_status():
                print("❌ 登录状态检查失败")
                return False
            print()

            # 6. 获取考试页面URL
            print("步骤6: 获取考试页面URL...")
            print("正在实际请求考试页面，等待跳转...")
            exam_page_url = self.get_exam_page_url(validate_str)
            if exam_page_url:
                print(f"✅ 成功获取考试页面URL")
                print(f"最终考试页面URL: {exam_page_url}")
                print()

                # 验证URL是否包含预期的考试页面标识
                if "reVersionTestStartNew" in exam_page_url:
                    print("✅ 确认获取到真正的考试页面URL")
                else:
                    print("⚠️ 获取到的URL可能不是最终的考试页面")

                print()
                print("🎉 考试滑块验证码处理流程完成！")
                print("现在可以使用获取到的URL访问考试页面。")

                # 保存最终URL到文件
                try:
                    with open("final_exam_url.txt", "w", encoding="utf-8") as f:
                        f.write(exam_page_url)
                    print("已保存最终考试URL到 final_exam_url.txt")
                except:
                    pass

                return True
            else:
                print("❌ 获取考试页面URL失败")
                return False

        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='超星学习通考试滑块验证码处理脚本')
    parser.add_argument('-u', '--username', required=True, help='学习通用户名')
    parser.add_argument('-p', '--password', required=True, help='学习通密码')
    parser.add_argument('--params', required=True, help='考试参数，格式：examId|courseId|classId|cpi')
    
    args = parser.parse_args()
    
    try:
        # 创建处理器
        processor = ExamCaptchaProcessor(args.username, args.password, args.params)
        
        # 运行处理流程
        success = processor.run()
        
        if success:
            print("\n🎉 考试滑块验证码处理完成！")
        else:
            print("\n❌ 考试滑块验证码处理失败！")
            sys.exit(1)
            
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
